

# Create a custom config



from default_config import DEFAULT_CONFIG
from graph_framework import TradingAgentsGraph


config = DEFAULT_CONFIG.copy()
# config["should_debate_count"] = 1  # Increase debate rounds
# config["online_tools"] = True  # Increase debate rounds

# Initialize with custom config
ta = TradingAgentsGraph(debug=True, config=config)

# forward propagate
final_state = ta.build("帮我从需求完成量和需求完成规模对本月数据进行洞察分析")

# investment_plan = final_state.get("investment_plan", "")
# print("调查讨论状态:")
# print(investment_plan)
# print("\n" + "="*50 + "\n")

# 输出最终状态
# print("最终状态:")
# print(final_state)