from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder
import time
import json
import csv
import logging
import os

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_rewrite_node(llm):
    def rewrite_node(state):
        user_input = state["text"]

        # 使用相对路径
        csv_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data', '指标主题.csv')
        system_message = (
            "指标主题：" + str([row for row in csv.reader(open(csv_path, 'r', encoding='utf-8'))])
        )

        prompt = ChatPromptTemplate.from_messages([
        ("system", "你是一个用户意图识别大师。请根据下面资料中的指标名称和页面展示指标描述，判断是否需要重写用户输入，如果需要则重写。判断的标准如下：\n \
            1.指标名称是否是资料中的指标名称，如果不是，则判断用户的输入符合指标描述或者近似于指标名称，如果是，则把相应的名称改为指标名称\n \
            2.若用户指出了非常明确的指标名称，则不需要改写，直接输出用户的原本输入\n\
            3.若用户输入和页面展示指标描述相似，则改写为指标名称\n\
            资料：\
            {system_message}\n\
            只回答改写后的结果，不要其他解释。"),
        ("user", "{user_input}")
    ])
        
        prompt = prompt.partial(system_message=system_message, user_input=user_input)

        chain = prompt | llm

        try:
            result = chain.invoke()
            print(f"调试信息 - chain.invoke() 结果: {result}")
            print(f"用户问题是: {result}")

            return {
                "messages": [result],
                "rewrite": result,
            }
        except Exception as e:
            # 错误处理
            error_message = f"Error in fundamentals analyst: {str(e)}"
            return {
                "rewrite": error_message,
            }

    return rewrite_node
