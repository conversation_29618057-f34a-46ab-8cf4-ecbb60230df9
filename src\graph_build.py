
# TradingAgents/graph/setup.py

from typing import Dict, Any
from langchain_openai import ChatOpenAI
from langgraph.graph import <PERSON><PERSON>, StateGraph, START
from langgraph.prebuilt import ToolNode

from node.rewrite import create_rewrite_node
from utils.state import AgentState
from utils.tools import Toolkit, create_msg_delete




class GraphSetup:
    """Handles the setup and configuration of the agent graph."""

    def __init__(
        self,
        quick_thinking_llm: ChatOpenAI,
        deep_thinking_llm: ChatOpenAI,
        toolkit: Toolkit,
        tool_nodes: Dict[str, ToolNode],
    ):
        """Initialize with required components."""
        self.quick_thinking_llm = quick_thinking_llm
        self.deep_thinking_llm = deep_thinking_llm
        self.toolkit = toolkit
        self.tool_nodes = tool_nodes

    def setup_graph(self):
        """Set up and compile the agent workflow graph with fundamentals and news analysts."""

        # # Always create news analyst
        rewrite_node = create_rewrite_node(
            self.quick_thinking_llm
        )
        delete_node = create_msg_delete()


        # Create workflow
        workflow = StateGraph(AgentState)

        # # Add analyst nodes to the graph
        workflow.add_node("rewrite node", rewrite_node)
        # workflow.add_node("Msg Clear News", delete_nodes["news"])
        
        # workflow.add_node("Fundamentals Analyst", analyst_nodes["fundamentals"])
        # workflow.add_node("Msg Clear Fundamentals", delete_nodes["fundamentals"])
        # workflow.add_node("tools_fundamentals", tool_nodes["fundamentals"])


        # Define edges - fixed sequence: news -> fundamentals -> Bull Researcher
        workflow.add_edge(START, "rewrite node")

        # # Fundamentals analyst flow
        # workflow.add_conditional_edges(
        #     "Fundamentals Analyst",
        #     getattr(self.conditional_logic, "should_continue_fundamentals"),
        #     ["tools_fundamentals", "Msg Clear Fundamentals"],
        # )
        # workflow.add_edge("tools_fundamentals", "Fundamentals Analyst")
        # workflow.add_edge("Msg Clear Fundamentals", "News Analyst")
        # # workflow.add_edge("Msg Clear Fundamentals", "News Analyst")

        # # # News analyst flow
        # workflow.add_conditional_edges(
        #     "News Analyst",
        #     getattr(self.conditional_logic, "should_continue_news"),
        #     ["tools_news", "Msg Clear News"],
        # )
        # workflow.add_edge("tools_news", "News Analyst")
        # workflow.add_edge("Msg Clear News", "Bull Researcher")

        # # Add remaining edges
        # workflow.add_conditional_edges(
        #     "Bull Researcher",
        #     self.conditional_logic.should_continue_debate,
        #     {
        #         "Bear Researcher": "Bear Researcher",
        #         # "End":END
        #         "Research Manager": "Research Manager",
        #     },
        # )
        # workflow.add_conditional_edges(
        #     "Bear Researcher",
        #     self.conditional_logic.should_continue_debate,
        #     {
        #         "Bull Researcher": "Bull Researcher",
        #         # "End":END
        #         "Research Manager": "Research Manager",
        #     },
        # )
        workflow.add_edge("rewrite node", END)

        # Compile and return
        return workflow.compile()
