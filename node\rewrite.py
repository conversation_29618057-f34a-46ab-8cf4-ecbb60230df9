from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder
import time
import json
import csv
import logging
import os
from datetime import datetime

# logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
# logger = logging.getLogger(__name__)

def create_rewrite_node(llm):
    def rewrite_node(state):
        user_input = state["question"]


        # 获取当前年月信息
        now = datetime.now()
        current_year = now.year
        current_month = now.month

        # 计算上个月
        if current_month == 1:
            last_month = 12
            last_year = current_year - 1
        else:
            last_month = current_month - 1
            last_year = current_year
        last_year_month = f"{last_year}年{last_month}月"


        # 读取并解析CSV数据
        csv_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data', '指标主题.csv')

        # 解析CSV数据为结构化格式
        indicators_info = []
        with open(csv_path, 'r', encoding='utf-8') as f:
            csv_reader = csv.DictReader(f)
            for row in csv_reader:
                if row.get('指标名称') and row.get('指标名称').strip():
                    indicators_info.append({
                        '指标主题': row.get('指标主题', ''),
                        '指标名称': row.get('指标名称', ''),
                        '页面展示指标描述': row.get('页面展示指标描述', ''),
                        '指标口径': row.get('指标口径', ''),
                        '指标统计区间': row.get('指标统计区间', ''),
                        '指标单位': row.get('指标单位', '')
                    })

        # 构建更清晰的系统消息
        system_message = f"""当前时间信息：
- 当前时间：{current_year_month}
- 上个月：{last_year_month}
- 当前年份：{current_year}年
- 当前月份：{current_month}月

可用的指标信息如下：
"""
        for i, indicator in enumerate(indicators_info, 1):
            system_message += f"\n{i}. 指标名称：{indicator['指标名称']}\n"
            system_message += f"   指标主题：{indicator['指标主题']}\n"
            system_message += f"   描述：{indicator['页面展示指标描述']}\n"
            system_message += f"   统计区间：{indicator['指标统计区间']}\n"
            if indicator['指标单位']:
                system_message += f"   单位：{indicator['指标单位']}\n"

        prompt = ChatPromptTemplate.from_messages([
        ("system", """你是一个专业的数据分析意图识别专家。你的任务是理解用户的问题，并将其转换为更精确、更专业的表述。

请根据以下指标库信息，分析用户的问题并进行优化改写：

{system_message}

改写规则：
1. **术语标准化**：将用户使用的非正式术语转换为标准指标名称
   - 例如："需求完成规模" → "需求复杂度"
   - 例如："代码量" → "代码当量"

2. **指标匹配**：如果用户提到的概念与某个具体指标相关，使用准确的指标名称
   - 匹配指标名称、描述或相关概念
   - 保持用户原始意图不变

3. **时间范围明确**：根据当前时间和指标统计区间，将模糊的时间表述转换为具体时间
   - "本月" → {current_year_month}
   - "上个月" → {last_year_month}
   - "今年" → {current_year}年
   - "最近" → 根据上下文具体化为具体月份或时间段

4. **分析维度补充**：如果用户的问题可以从多个维度分析，适当补充相关维度
   - 例如：不仅看数量，也看质量和效率

5. **保持原意**：改写后的问题应该保持用户的原始分析意图，只是表述更专业准确

请将用户的问题改写为更专业、更精确的表述。只输出改写后的结果，不要解释过程。"""),
        ("user", "用户问题：{user_input}")
    ])
        
        prompt = prompt.partial(system_message=system_message, user_input=user_input)

        # 打印完整的 prompt 内容
        print("=" * 50)
        print("发送给 LLM 的完整 prompt:")
        print("=" * 50)
        formatted_prompt = prompt.format()
        print(formatted_prompt)
        print("=" * 50)

        chain = prompt | llm

        try:
            result = chain.invoke({})
            # 提取文本内容
            if hasattr(result, 'content'):
                rewritten_text = result.content
            else:
                rewritten_text = str(result)

            return {
                "messages": [result],
                "rewrite_question": rewritten_text,
            }
        except Exception as e:
            # 错误处理
            error_message = f"Error in rewrite node: {str(e)}"
            print(f"错误: {error_message}")
            return {
                "messages": [("ai", error_message)],
                "rewrite_question": user_input,  # 如果出错，使用原始输入
            }

    return rewrite_node
