from typing import Annotated, Any, Dict, Sequence
from datetime import date, timedelta, datetime
from typing_extensions import TypedDict, Optional
from langchain_openai import ChatOpenAI
from langgraph.prebuilt import ToolNode
from langgraph.graph import END, StateGraph, START, MessagesState

class AgentState(MessagesState):
    company_name: Annotated[str, "Company name"]
    trade_date: Annotated[str, "What date we are trading at"]
    symbol: Annotated[str, "Symbol for the company"]

    sender: Annotated[str, "Agent that sent this message"]

    # research step
    sentiment_report: Annotated[str, "Report from the Social Media Analyst"]
    fundamentals_report: Annotated[str, "Report from the Fundamentals Researcher"]

    # researcher team discussion step
    debate_research_state: Annotated[
        DebateReserchState, "Current state of the debate on if to invest or not"
    ]
    investment_plan: Annotated[str, "Plan generated by the Analyst"]